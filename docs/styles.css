:root {
  --bg-color: #0a0a0a;
  --text-color: #e0e0e0;
  --accent-color: #ff3e3e;
  --secondary-color: #2a2a2a;
  --highlight-color: #3e3eff;
  --terminal-green: #00ff00;
  --terminal-blue: #00aaff;
  --terminal-yellow: #ffaa00;
  --terminal-red: #ff3e3e;
  --terminal-purple: #aa00ff;
  --card-bg: rgba(20, 20, 20, 0.7);
  --card-border: rgba(80, 80, 80, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'JetBrains Mono', monospace;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.9)),
    url('/FlameOS.png');
  background-size: cover;
  background-attachment: fixed;
  background-position: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

header {
  padding: 20px 0;
  border-bottom: 1px solid var(--accent-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.node-logo {
  width: 50px;
  height: 50px;
}

h1 {
  font-size: 2rem;
  color: var(--accent-color);
}

.version {
  font-size: 0.8rem;
  color: var(--terminal-green);
  vertical-align: super;
}

nav ul {
  display: flex;
  list-style: none;
  gap: 20px;
}

nav a {
  color: var(--text-color);
  text-decoration: none;
  padding: 5px 10px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

nav a:hover {
  background-color: var(--accent-color);
  color: var(--bg-color);
}

section {
  margin-bottom: 60px;
  padding: 20px;
  background-color: rgba(10, 10, 10, 0.8);
  border-radius: 8px;
  border: 1px solid var(--card-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h2 {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: var(--terminal-blue);
  border-bottom: 1px solid var(--terminal-blue);
  padding-bottom: 10px;
}

h3 {
  font-size: 1.4rem;
  margin-bottom: 15px;
  color: var(--terminal-yellow);
}

h4 {
  font-size: 1.2rem;
  margin-bottom: 10px;
  color: var(--terminal-green);
}

p {
  margin-bottom: 15px;
}

.feature-grid, .bright-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.feature-card, .bright-data-card, .terminal-feature {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 8px;
  padding: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover, .bright-data-card:hover, .terminal-feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.bright-data-card h3 {
  color: var(--terminal-purple);
}

.bright-data-card code {
  display: block;
  background-color: var(--secondary-color);
  padding: 10px;
  border-radius: 4px;
  margin-top: 10px;
  font-size: 0.9rem;
  overflow-x: auto;
}

.command-section {
  margin-bottom: 30px;
}

.command-table {
  border: 1px solid var(--card-border);
  border-radius: 8px;
  overflow: hidden;
}

.command-row {
  display: grid;
  grid-template-columns: 1fr 2fr;
  border-bottom: 1px solid var(--card-border);
}

.command-row:last-child {
  border-bottom: none;
}

.command, .description {
  padding: 10px 15px;
}

.command {
  background-color: var(--secondary-color);
  font-weight: bold;
  color: var(--terminal-green);
}

.terminal-features {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.hackathon-enhancements {
  margin-top: 40px;
  border: 1px solid var(--terminal-yellow);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 40px;
}

.hackathon-enhancements h3 {
  background-color: var(--terminal-yellow);
  color: var(--bg-color);
  padding: 15px;
  margin-bottom: 0;
}

.hackathon-enhancements ul {
  padding: 20px;
  background-color: rgba(255, 170, 0, 0.1);
  list-style-position: inside;
}

.hackathon-enhancements li {
  margin-bottom: 10px;
}

.hackathon-enhancements p {
  padding: 0 20px 20px 20px;
  background-color: rgba(255, 170, 0, 0.1);
  margin-bottom: 0;
}

.hackathon-link {
  color: var(--terminal-yellow);
  text-decoration: none;
  border: 1px solid var(--terminal-yellow);
  padding: 5px 10px;
  border-radius: 4px;
  display: inline-block;
  transition: all 0.3s ease;
}

.hackathon-link:hover {
  background-color: var(--terminal-yellow);
  color: var(--bg-color);
}

.data-warfare-guide {
  margin-top: 40px;
  border: 1px solid var(--terminal-red);
  border-radius: 8px;
  overflow: hidden;
}

.data-warfare-guide h3 {
  background-color: var(--terminal-red);
  color: var(--bg-color);
  padding: 15px;
  margin-bottom: 0;
}

.guide-content {
  padding: 20px;
  background-color: rgba(255, 62, 62, 0.1);
}

footer {
  text-align: center;
  padding: 30px 0;
  margin-top: 60px;
  border-top: 1px solid var(--accent-color);
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.footer-links a {
  color: var(--terminal-blue);
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 20px;
  }

  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }

  .command-row {
    grid-template-columns: 1fr;
  }

  .command {
    border-bottom: 1px solid var(--card-border);
  }
}
