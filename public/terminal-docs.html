<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DataOps Terminal Documentation</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
  <style>
    /* Terminal Intro Styles */
    .terminal-intro {
      font-family: 'JetBrains Mono', monospace;
      color: #3b82f6;
      background: #0f172a;
      padding: 2rem;
      height: 100vh;
      width: 100vw;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 1000;
      overflow: hidden;
      white-space: pre-wrap;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      animation: fadeIn 1s ease-out;
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }

    @keyframes flicker {
      0%   { opacity: 0.1; }
      20%  { opacity: 1; }
      40%  { opacity: 0.2; }
      60%  { opacity: 1; }
      80%  { opacity: 0.3; }
      100% { opacity: 1; }
    }

    .flicker {
      animation: flicker 0.3s infinite;
    }

    .fade-out {
      animation: fadeOut 0.5s forwards;
    }

    /* Main Documentation Styles */
    :root {
      --bg-color: #121212;
      --text-color: #e0e0e0;
      --accent-color: #3b82f6;
      --secondary-color: #1e293b;
      --terminal-green: #10b981;
      --terminal-blue: #3b82f6;
      --terminal-yellow: #f59e0b;
      --terminal-red: #ef4444;
      --terminal-purple: #8b5cf6;
      --pro-primary: #3b82f6;
      --pro-secondary: #64748b;
      --pro-bg-dark: #0f172a;
      --pro-text-dark: #e2e8f0;
      --pro-text-muted: #94a3b8;
      --pro-border: #334155;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'JetBrains Mono', monospace;
      background-color: var(--bg-color);
      color: var(--text-color);
      line-height: 1.6;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
      overflow-y: auto;
    }

    .terminal-header {
      background-color: #1a1a1a;
      padding: 8px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--accent-color);
    }

    .terminal-title {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .terminal-title h1 {
      font-size: 1.2rem;
      color: var(--accent-color);
    }

    .terminal-controls {
      display: flex;
      gap: 10px;
    }

    .terminal-controls a {
      color: var(--text-color);
      text-decoration: none;
      padding: 4px 8px;
      border: 1px solid var(--terminal-blue);
      border-radius: 4px;
      font-size: 0.8rem;
      transition: all 0.2s ease;
    }

    .terminal-controls a:hover {
      background-color: var(--terminal-blue);
      color: var(--bg-color);
    }

    .terminal-controls .hackathon-link {
      border-color: var(--terminal-yellow);
      color: var(--terminal-yellow);
    }

    .terminal-controls .hackathon-link:hover {
      background-color: var(--terminal-yellow);
      color: var(--bg-color);
    }

    .search-bar {
      background-color: #1a1a1a;
      padding: 8px 16px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid var(--secondary-color);
    }

    .search-prompt {
      color: var(--terminal-green);
      margin-right: 8px;
      white-space: nowrap;
    }

    .search-input {
      flex: 1;
      background-color: transparent;
      border: none;
      color: var(--text-color);
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.9rem;
      outline: none;
    }

    .search-button {
      background-color: var(--secondary-color);
      border: 1px solid var(--terminal-blue);
      color: var(--terminal-blue);
      padding: 4px 8px;
      font-family: 'JetBrains Mono', monospace;
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .search-button:hover {
      background-color: var(--terminal-blue);
      color: var(--bg-color);
    }

    .terminal-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      background-color: var(--bg-color);
      max-height: calc(100vh - 120px); /* Adjust based on header and search bar height */
    }

    .section {
      margin-bottom: 30px;
    }

    .section-title {
      color: var(--terminal-blue);
      margin-bottom: 15px;
      border-bottom: 1px solid var(--terminal-blue);
      padding-bottom: 5px;
      font-size: 1.2rem;
    }

    .command-group {
      margin-bottom: 20px;
    }

    .command-group-title {
      color: var(--terminal-yellow);
      margin-bottom: 10px;
      font-size: 1.1rem;
    }

    .command-item {
      margin-bottom: 15px;
      padding-left: 15px;
      border-left: 2px solid var(--secondary-color);
    }

    .command-name {
      color: var(--terminal-green);
      font-weight: bold;
      margin-bottom: 5px;
    }

    .command-description {
      color: var(--text-color);
      margin-bottom: 5px;
      font-size: 0.9rem;
    }

    .command-example {
      color: var(--terminal-purple);
      background-color: rgba(170, 0, 255, 0.1);
      padding: 5px;
      border-radius: 4px;
      font-size: 0.85rem;
      margin-top: 5px;
    }

    .guide-section {
      margin-bottom: 30px;
    }

    .guide-title {
      color: var(--terminal-red);
      margin-bottom: 15px;
      border-bottom: 1px solid var(--terminal-red);
      padding-bottom: 5px;
      font-size: 1.2rem;
    }

    .guide-content {
      color: var(--text-color);
      font-size: 0.9rem;
    }

    .guide-subtitle {
      color: var(--terminal-purple);
      margin-top: 15px;
      margin-bottom: 10px;
      font-size: 1rem;
    }

    .node-logo {
      position: fixed;
      bottom: 10px;
      right: 10px;
      width: 50px;
      height: 50px;
      opacity: 0.3;
      pointer-events: none;
    }

    .hidden {
      display: none;
    }

    /* Section navigation */
    .section-nav {
      margin-bottom: 30px;
    }

    .section-links {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 15px;
    }

    .section-link {
      background-color: var(--secondary-color);
      color: var(--terminal-blue);
      padding: 8px 12px;
      border-radius: 4px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid var(--terminal-blue);
    }

    .section-link:hover {
      background-color: var(--terminal-blue);
      color: var(--bg-color);
    }

    /* Loading indicator */
    .loading-indicator {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      color: var(--terminal-yellow);
    }

    .loading-text {
      position: relative;
    }

    .loading-text:after {
      content: '';
      animation: loading-dots 1.5s infinite;
    }

    @keyframes loading-dots {
      0% { content: ''; }
      25% { content: '.'; }
      50% { content: '..'; }
      75% { content: '...'; }
      100% { content: ''; }
    }

    /* Modal styles */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .modal.open {
      opacity: 1;
      visibility: visible;
    }

    .modal-content {
      background-color: var(--bg-color);
      border: 2px solid var(--terminal-blue);
      border-radius: 4px;
      padding: 20px;
      max-width: 600px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      position: relative;
      scrollbar-width: thin;
      scrollbar-color: var(--secondary-color) var(--bg-color);
    }

    .close-button {
      position: absolute;
      top: 10px;
      right: 10px;
      background: none;
      border: none;
      color: var(--terminal-red);
      font-size: 24px;
      cursor: pointer;
    }

    /* Shortcuts modal */
    .shortcuts-modal .modal-content h2 {
      color: var(--terminal-blue);
      margin-bottom: 20px;
      border-bottom: 1px solid var(--terminal-blue);
      padding-bottom: 10px;
    }

    .shortcuts-list {
      list-style: none;
      padding: 0;
    }

    .shortcuts-list li {
      display: flex;
      margin-bottom: 10px;
      padding: 5px;
      border-bottom: 1px solid var(--secondary-color);
    }

    .key-combo {
      background-color: var(--secondary-color);
      color: var(--terminal-yellow);
      padding: 2px 8px;
      border-radius: 4px;
      margin-right: 10px;
      font-family: 'JetBrains Mono', monospace;
      min-width: 100px;
      display: inline-block;
    }

    .shortcut-description {
      color: var(--text-color);
    }

    /* Scrollbar styling */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: var(--bg-color);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--secondary-color);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--accent-color);
    }
  </style>
</head>
<body>
  <div id="terminal-intro" class="terminal-intro">
    <pre id="intro-text" class="flicker"></pre>
  </div>

  <div id="main-content" style="display: flex; flex-direction: column; height: 100vh; overflow: hidden;">
    <div class="terminal-header">
      <div class="terminal-title">
        <h1>DataOps Terminal Documentation</h1>
      </div>
      <div class="terminal-controls">
        <a href="/">Home</a>
        <a href="/cli">Terminal</a>
        <a href="/index.html">Main Docs</a>
        <a href="/hackathon-reference.html" class="hackathon-link">Hackathon Reference</a>
      </div>
    </div>

  <div class="search-bar">
    <span class="search-prompt">dataops@docs:~$ find</span>
    <input type="text" class="search-input" id="search-input" placeholder="Type to search documentation..." autocomplete="off">
    <button class="search-button" id="search-button">Search</button>
  </div>

  <div class="terminal-content" id="terminal-content">
    <!-- Documentation content will be loaded here -->
  </div>

  <img data-src="/NODE.png" alt="NODE Logo" class="node-logo lazy-image">
  </div><!-- End of main-content -->

  <!-- Image optimization styles -->
  <style>
    .lazy-image {
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
      background-color: #1a1a1a;
    }

    .lazy-image.loaded {
      opacity: 1;
    }
  </style>

  <script>
    // Documentation content
    const documentation = {
      commands: [
        {
          group: "Core Commands",
          items: [
            {
              name: "!help",
              description: "Show available commands and the DataOps Guide to Web Data Operations",
              example: "!help"
            },
            {
              name: "!mission",
              description: "Set current mission name or create a new mission with objective",
              example: "!mission INFILTRATE -o \"Extract data from target system\""
            },
            {
              name: "!status",
              description: "Show current system status including internet access, modes, and mission",
              example: "!status"
            },
            {
              name: "!save",
              description: "Save session log as Markdown or JSON file",
              example: "!save md\n!save json"
            },
            {
              name: "!clear",
              description: "Clear the terminal screen",
              example: "!clear"
            }
          ]
        },
        {
          group: "Mode Controls",
          items: [
            {
              name: "!internet",
              description: "Enable or disable internet access for web commands",
              example: "!internet on\n!internet off"
            },
            {
              name: "!nlp",
              description: "Enable or disable natural language command parsing",
              example: "!nlp on\n!nlp off"
            },
            {
              name: "!autonomy",
              description: "Enable or disable autonomy mode (auto-confirm commands)",
              example: "!autonomy on\n!autonomy off"
            }
          ]
        },
        {
          group: "Security Commands",
          items: [
            {
              name: "!airlock",
              description: "Block or allow all outbound HTTP requests",
              example: "!airlock on\n!airlock off"
            },
            {
              name: "!encrypt",
              description: "Enable or disable scroll encryption for logs",
              example: "!encrypt on\n!encrypt off"
            },
            {
              name: "!decrypt-scroll",
              description: "Decrypt an encrypted scroll file",
              example: "!decrypt-scroll mission_log_encrypted.json"
            },
            {
              name: "!passphrase",
              description: "Set encryption passphrase for scroll encryption",
              example: "!passphrase \"my-secure-key\""
            }
          ]
        }
      ],
      webCommands: [
        {
          group: "Web Commands",
          items: [
            {
              name: "!recon",
              description: "Download site HTML and source for analysis",
              example: "!recon https://example.com"
            },
            {
              name: "!extract-doi",
              description: "Extract metadata from scientific articles using DOI",
              example: "!extract-doi --doi \"10.1126/sciadv.adu9368\""
            },
            {
              name: "!scrape",
              description: "Start crawl of target site for specific keyword",
              example: "!scrape \"artificial intelligence\" https://example.com"
            }
          ]
        },
        {
          group: "Extended Recon Suite",
          items: [
            {
              name: "!net-scan",
              description: "Perform DNS/IP scan and analysis on a domain or IP",
              example: "!net-scan example.com"
            },
            {
              name: "!git-harvest",
              description: "Crawl GitHub repositories and metadata for an organization or user",
              example: "!git-harvest octocat"
            },
            {
              name: "!scan",
              description: "Scan academic paper metadata with threat detection",
              example: "!scan --doi \"10.1038/s41586-021-03819-2\" --output paper_analysis.json"
            },
            {
              name: "!science-scan",
              description: "Search Science.org for research articles on specific topics",
              example: "!science-scan --query \"quantum computing\" --limit 5 --output quantum_research.json"
            }
          ]
        }
      ],
      brightData: [
        {
          group: "Bright Data MCP Commands",
          items: [
            {
              name: "!dataops discover",
              description: "Find relevant content across the web using Bright Data's infrastructure",
              example: "!dataops discover --query \"latest AI research\" --output ai_content.json"
            },
            {
              name: "!dataops access",
              description: "Access complex websites with rendering and authentication support",
              example: "!dataops access --url \"https://example.com/login\" --render --auth --output page.json"
            },
            {
              name: "!dataops extract",
              description: "Extract structured data from websites using specified schema",
              example: "!dataops extract --url \"https://example.com/blog\" --schema \"title,author,date,content\" --output blog_data.json"
            },
            {
              name: "!dataops interact",
              description: "Interact with websites by simulating user actions",
              example: "!dataops interact --url \"https://example.com/search\" --simulate \"search data extraction\" --output search_results.json"
            },
            {
              name: "!dataops collect",
              description: "Run a pre-configured Data Collector for specialized targets",
              example: "!dataops collect --target \"science_papers\" --params \"keyword=AI,limit=10\" --output science_papers.json"
            },
            {
              name: "!dataops ops",
              description: "Open Bright Data Operations Panel to view and manage operations",
              example: "!dataops ops"
            }
          ]
        }
      ],
      guide: {
        title: "DataOps Terminal Guide to Web Data Operations",
        content: "Welcome to the DataOps Terminal guide to web data operations. This guide provides professional techniques for efficiently collecting, processing, and analyzing data from web sources using modern tools and methodologies.",
        sections: [
          {
            subtitle: "Discover",
            content: "Efficient content discovery across the web requires well-configured web crawlers. Tools like Scrapy or Apache Nutch provide robust frameworks for systematic data collection. Configure your crawlers with precise parameters to ensure you gather only relevant information. Implement proper filtering mechanisms and keyword optimization to maintain high-quality results and avoid collecting unnecessary data."
          },
          {
            subtitle: "Access",
            content: "Accessing complex websites often requires specialized techniques. Utilize proxy management systems to handle rate limiting and ensure reliable access. For sites with authentication requirements, tools like Selenium or Playwright can automate login processes. Always respect robots.txt directives and implement proper request throttling to maintain ethical access patterns and avoid overloading target servers."
          },
          {
            subtitle: "Extract",
            content: "Data extraction requires selecting the right tools for each scenario. For HTML parsing, libraries like Beautiful Soup and lxml offer efficient solutions, while JSON APIs provide structured data access. When dealing with JavaScript-heavy sites, headless browsers like Puppeteer or Playwright can render the full page before extraction. Design your extraction patterns to be resilient against site structure changes."
          },
          {
            subtitle: "Interact",
            content: "Interactive data collection involves simulating user behavior on dynamic websites. Modern browser automation tools allow you to programmatically navigate complex interfaces, fill forms, and trigger events. Implement wait strategies to handle asynchronous content loading, and create robust selectors that can withstand minor UI changes. For complex interactions, consider recording and parameterizing user flows."
          },
          {
            subtitle: "Best Practices",
            content: "Always maintain ethical standards in your data operations. Respect website terms of service, implement proper rate limiting, and store only the data you need. Document your data collection methodologies, maintain data provenance records, and implement proper data governance. Regular auditing of your collection processes ensures continued compliance with evolving regulations and industry standards."
          }
        ]
      }
    };

    // Function to render documentation with lazy loading
    function renderDocumentation() {
      const content = document.getElementById('terminal-content');
      content.innerHTML = '';

      // Create section navigation
      const navSection = document.createElement('div');
      navSection.className = 'section-nav';
      navSection.innerHTML = `
        <h2 class="section-title">Documentation Sections</h2>
        <div class="section-links">
          <a href="#command-reference" class="section-link" data-section="command-reference">Command Reference</a>
          <a href="#web-operations" class="section-link" data-section="web-operations">Web Operations</a>
          <a href="#bright-data" class="section-link" data-section="bright-data">Bright Data Integration</a>
          <a href="#data-warfare" class="section-link" data-section="data-warfare">Web Data Operations Guide</a>
        </div>
      `;
      content.appendChild(navSection);

      // Create placeholder sections
      const sections = [
        { id: 'command-reference', title: 'Command Reference', loaded: false },
        { id: 'web-operations', title: 'Web Operations', loaded: false },
        { id: 'bright-data', title: 'Bright Data Integration', loaded: false },
        { id: 'data-warfare', title: 'DataOps Terminal Guide to Web Data Operations', loaded: false }
      ];

      sections.forEach(section => {
        const sectionElement = document.createElement('div');
        sectionElement.id = section.id;
        sectionElement.className = 'section';
        sectionElement.innerHTML = `
          <h2 class="section-title">${section.title}</h2>
          <div class="section-content" id="${section.id}-content">
            <div class="loading-indicator">
              <span class="loading-text">Loading ${section.title}...</span>
            </div>
          </div>
        `;
        content.appendChild(sectionElement);
      });

      // Set up intersection observer for lazy loading
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const sectionId = entry.target.id;
            if (!sections.find(s => s.id === sectionId).loaded) {
              loadSectionContent(sectionId);
              sections.find(s => s.id === sectionId).loaded = true;
            }
          }
        });
      }, { threshold: 0.1 });

      // Observe each section
      sections.forEach(section => {
        const element = document.getElementById(section.id);
        if (element) observer.observe(element);
      });

      // Load the first section immediately
      loadSectionContent('command-reference');
      sections[0].loaded = true;

      // Add click handlers for section navigation
      document.querySelectorAll('.section-link').forEach(link => {
        link.addEventListener('click', (e) => {
          e.preventDefault();
          const sectionId = e.target.getAttribute('data-section');
          document.getElementById(sectionId).scrollIntoView({ behavior: 'smooth' });

          // Load the section content if not already loaded
          if (!sections.find(s => s.id === sectionId).loaded) {
            loadSectionContent(sectionId);
            sections.find(s => s.id === sectionId).loaded = true;
          }
        });
      });
    }

    // Function to load section content
    function loadSectionContent(sectionId) {
      const contentElement = document.getElementById(`${sectionId}-content`);
      if (!contentElement) return;

      // Show loading indicator
      contentElement.innerHTML = `
        <div class="loading-indicator">
          <span class="loading-text">Loading content...</span>
        </div>
      `;

      // Simulate network delay for demonstration
      setTimeout(() => {
        switch(sectionId) {
          case 'command-reference':
            renderCommandReference(contentElement);
            break;
          case 'web-operations':
            renderWebOperations(contentElement);
            break;
          case 'bright-data':
            renderBrightData(contentElement);
            break;
          case 'data-warfare':
            renderDataWarfareGuide(contentElement);
            break;
        }
      }, 300); // Small delay to show loading indicator
    }

    // Render Command Reference section
    function renderCommandReference(container) {
      container.innerHTML = '';

      documentation.commands.forEach(group => {
        const groupElement = document.createElement('div');
        groupElement.className = 'command-group';
        groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

        group.items.forEach(item => {
          const itemElement = document.createElement('div');
          itemElement.className = 'command-item';
          itemElement.innerHTML = `
            <div class="command-name">${item.name}</div>
            <div class="command-description">${item.description}</div>
            <div class="command-example">${item.example}</div>
          `;
          groupElement.appendChild(itemElement);
        });

        container.appendChild(groupElement);
      });
    }

    // Render Web Operations section
    function renderWebOperations(container) {
      container.innerHTML = '';

      documentation.webCommands.forEach(group => {
        const groupElement = document.createElement('div');
        groupElement.className = 'command-group';
        groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

        group.items.forEach(item => {
          const itemElement = document.createElement('div');
          itemElement.className = 'command-item';
          itemElement.innerHTML = `
            <div class="command-name">${item.name}</div>
            <div class="command-description">${item.description}</div>
            <div class="command-example">${item.example}</div>
          `;
          groupElement.appendChild(itemElement);
        });

        container.appendChild(groupElement);
      });
    }

    // Render Bright Data section
    function renderBrightData(container) {
      container.innerHTML = '';

      documentation.brightData.forEach(group => {
        const groupElement = document.createElement('div');
        groupElement.className = 'command-group';
        groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

        group.items.forEach(item => {
          const itemElement = document.createElement('div');
          itemElement.className = 'command-item';
          itemElement.innerHTML = `
            <div class="command-name">${item.name}</div>
            <div class="command-description">${item.description}</div>
            <div class="command-example">${item.example}</div>
          `;
          groupElement.appendChild(itemElement);
        });

        container.appendChild(groupElement);
      });
    }

    // Render Data Warfare Guide section
    function renderDataWarfareGuide(container) {
      container.innerHTML = '';

      const guideElement = document.createElement('div');
      guideElement.className = 'guide-content';
      guideElement.innerHTML = `<p>${documentation.guide.content}</p>`;
      container.appendChild(guideElement);

      documentation.guide.sections.forEach(section => {
        const sectionElement = document.createElement('div');
        sectionElement.innerHTML = `
          <h3 class="guide-subtitle">${section.subtitle}</h3>
          <p class="guide-content">${section.content}</p>
        `;
        container.appendChild(sectionElement);
      });
    }

    // Function to search documentation
    function searchDocumentation(query) {
      if (!query) {
        renderDocumentation();
        return;
      }

      query = query.toLowerCase();
      const content = document.getElementById('terminal-content');
      content.innerHTML = '';

      const searchResultsSection = document.createElement('div');
      searchResultsSection.className = 'section';
      searchResultsSection.innerHTML = `<h2 class="section-title">Search Results for: "${query}"</h2>`;

      let resultsFound = false;

      // Search in commands
      documentation.commands.forEach(group => {
        const matchingItems = group.items.filter(item =>
          item.name.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.example.toLowerCase().includes(query)
        );

        if (matchingItems.length > 0) {
          resultsFound = true;
          const groupElement = document.createElement('div');
          groupElement.className = 'command-group';
          groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

          matchingItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'command-item';
            itemElement.innerHTML = `
              <div class="command-name">${item.name}</div>
              <div class="command-description">${item.description}</div>
              <div class="command-example">${item.example}</div>
            `;
            groupElement.appendChild(itemElement);
          });

          searchResultsSection.appendChild(groupElement);
        }
      });

      // Search in web commands
      documentation.webCommands.forEach(group => {
        const matchingItems = group.items.filter(item =>
          item.name.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.example.toLowerCase().includes(query)
        );

        if (matchingItems.length > 0) {
          resultsFound = true;
          const groupElement = document.createElement('div');
          groupElement.className = 'command-group';
          groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

          matchingItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'command-item';
            itemElement.innerHTML = `
              <div class="command-name">${item.name}</div>
              <div class="command-description">${item.description}</div>
              <div class="command-example">${item.example}</div>
            `;
            groupElement.appendChild(itemElement);
          });

          searchResultsSection.appendChild(groupElement);
        }
      });

      // Search in Bright Data commands
      documentation.brightData.forEach(group => {
        const matchingItems = group.items.filter(item =>
          item.name.toLowerCase().includes(query) ||
          item.description.toLowerCase().includes(query) ||
          item.example.toLowerCase().includes(query)
        );

        if (matchingItems.length > 0) {
          resultsFound = true;
          const groupElement = document.createElement('div');
          groupElement.className = 'command-group';
          groupElement.innerHTML = `<h3 class="command-group-title">${group.group}</h3>`;

          matchingItems.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'command-item';
            itemElement.innerHTML = `
              <div class="command-name">${item.name}</div>
              <div class="command-description">${item.description}</div>
              <div class="command-example">${item.example}</div>
            `;
            groupElement.appendChild(itemElement);
          });

          searchResultsSection.appendChild(groupElement);
        }
      });

      // Search in guide
      const guideContent = documentation.guide.content.toLowerCase();
      let guideMatches = guideContent.includes(query);

      documentation.guide.sections.forEach(section => {
        if (section.subtitle.toLowerCase().includes(query) || section.content.toLowerCase().includes(query)) {
          guideMatches = true;
        }
      });

      if (guideMatches) {
        resultsFound = true;
        const guideElement = document.createElement('div');
        guideElement.className = 'command-group';
        guideElement.innerHTML = `<h3 class="command-group-title">Data Warfare Guide</h3>`;

        const itemElement = document.createElement('div');
        itemElement.className = 'command-item';
        itemElement.innerHTML = `
          <div class="command-name">${documentation.guide.title}</div>
          <div class="command-description">Guide to web data operations and techniques</div>
          <div class="command-example">!help</div>
        `;
        guideElement.appendChild(itemElement);

        searchResultsSection.appendChild(guideElement);
      }

      if (!resultsFound) {
        searchResultsSection.innerHTML += `
          <div class="command-group">
            <div class="command-item">
              <div class="command-description" style="color: var(--terminal-red);">No results found for "${query}". Try a different search term.</div>
            </div>
          </div>
        `;
      }

      content.appendChild(searchResultsSection);
    }

    // Initialize the documentation
    document.addEventListener('DOMContentLoaded', () => {
      renderDocumentation();

      // Set up search functionality
      const searchInput = document.getElementById('search-input');
      const searchButton = document.getElementById('search-button');

      searchButton.addEventListener('click', () => {
        searchDocumentation(searchInput.value);
      });

      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          searchDocumentation(searchInput.value);
        }
      });

      // Set up lazy loading for images
      setupLazyLoading();
    });

    // Image lazy loading
    function setupLazyLoading() {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              const dataSrc = img.getAttribute('data-src');

              if (dataSrc) {
                img.setAttribute('src', dataSrc);
                img.removeAttribute('data-src');
                img.classList.remove('lazy-image');
                img.classList.add('loaded');
              }

              observer.unobserve(img);
            }
          });
        });

        // Find all images with the lazy-image class
        document.querySelectorAll('img.lazy-image').forEach(img => {
          imageObserver.observe(img);
        });
      } else {
        // Fallback for browsers that don't support IntersectionObserver
        document.querySelectorAll('img.lazy-image').forEach(img => {
          const dataSrc = img.getAttribute('data-src');
          if (dataSrc) {
            img.setAttribute('src', dataSrc);
            img.removeAttribute('data-src');
            img.classList.remove('lazy-image');
            img.classList.add('loaded');
          }
        });
      }
    }

    // Terminal Intro Animation
    document.addEventListener('DOMContentLoaded', () => {
      const introElement = document.getElementById('terminal-intro');
      const introTextElement = document.getElementById('intro-text');
      const mainContent = document.getElementById('main-content');

      // Hide main content initially
      mainContent.style.display = 'none';

      const introLines = [
        'dataops@terminal:~$ sudo ./initialize_docs.sh',
        'Initializing DataOps Terminal [v1.0.7] ...',
        'Connecting to Bright Data MCP server...',
        'Loading documentation resources... ',
        'Preparing documentation interface → /docs/',
        'Command index loaded.',
        '████ SYSTEM ONLINE ████',
        '',
        'dataops@docs:~$'
      ];

      let currentLine = 0;
      let displayedText = '';

      function typeNextLine() {
        if (currentLine < introLines.length) {
          displayedText += introLines[currentLine] + '\n';
          introTextElement.textContent = displayedText;
          currentLine++;
          setTimeout(typeNextLine, 600);
        } else {
          // Animation complete, show main content
          setTimeout(() => {
            introElement.classList.add('fade-out');
            setTimeout(() => {
              introElement.style.display = 'none';
              mainContent.style.display = 'block';
              // Initialize documentation
              renderDocumentation();
              // Initialize keyboard shortcuts
              initializeKeyboardShortcuts();
            }, 500);
          }, 800);
        }
      }

      // Start the animation
      typeNextLine();
    });

    // Keyboard Shortcuts
    function initializeKeyboardShortcuts() {
      // Register keyboard shortcuts
      document.addEventListener('keydown', (event) => {
        // Skip if target is an input or textarea
        if (
          event.target instanceof HTMLInputElement ||
          event.target instanceof HTMLTextAreaElement ||
          (event.target instanceof HTMLElement && event.target.isContentEditable)
        ) {
          // Allow only the '?' shortcut in input fields
          if (event.key === '?' && event.shiftKey) {
            event.preventDefault();
            showShortcutsHelp();
          }
          return;
        }

        // Global shortcuts
        switch (event.key) {
          case '/':
            // Focus search
            event.preventDefault();
            document.getElementById('search-input')?.focus();
            break;

          case 'Escape':
            // Close any open modal
            const modal = document.querySelector('.modal.open');
            if (modal) {
              modal.remove();
            }
            break;

          case '?':
            // Show keyboard shortcuts help
            if (event.shiftKey) {
              event.preventDefault();
              showShortcutsHelp();
            }
            break;
        }
      });
    }

    // Show keyboard shortcuts help
    function showShortcutsHelp() {
      // Remove existing modal if any
      const existingModal = document.querySelector('.shortcuts-modal');
      if (existingModal) {
        existingModal.remove();
      }

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'modal shortcuts-modal open';

      // Modal content
      const modalContent = document.createElement('div');
      modalContent.className = 'modal-content';

      // Close button
      const closeButton = document.createElement('button');
      closeButton.className = 'close-button';
      closeButton.textContent = '×';
      closeButton.onclick = () => modal.remove();

      // Title
      const title = document.createElement('h2');
      title.textContent = 'Keyboard Shortcuts';

      // Shortcuts list
      const shortcutsList = document.createElement('ul');
      shortcutsList.className = 'shortcuts-list';

      // Define shortcuts
      const shortcuts = [
        { key: '/', description: 'Focus search' },
        { key: 'Shift + ?', description: 'Show keyboard shortcuts' },
        { key: 'Escape', description: 'Close modal' }
      ];

      // Add shortcuts to list
      shortcuts.forEach(shortcut => {
        const shortcutItem = document.createElement('li');

        const keyCombo = document.createElement('span');
        keyCombo.className = 'key-combo';
        keyCombo.textContent = shortcut.key;

        const description = document.createElement('span');
        description.className = 'shortcut-description';
        description.textContent = shortcut.description;

        shortcutItem.appendChild(keyCombo);
        shortcutItem.appendChild(description);
        shortcutsList.appendChild(shortcutItem);
      });

      // Assemble modal
      modalContent.appendChild(closeButton);
      modalContent.appendChild(title);
      modalContent.appendChild(shortcutsList);
      modal.appendChild(modalContent);

      // Add modal to document
      document.body.appendChild(modal);
    }
  </script>
</body>
</html>
