
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;

    --card: 0 0% 5%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 349 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 180 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 84.9%;

    --accent: 349 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 349 100% 50%;
    --input: 240 3.7% 15.9%;
    --ring: 349 100% 50%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-cyber-red/30 selection:text-cyber-cyan-bright;
  }

  html, body {
    @apply overflow-auto;
  }

  body {
    @apply bg-cyber-black text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-cyber-darkgray;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-cyber-red/70 rounded-full border border-cyber-red-dim animate-pulse;
  }

  /* Input and Button Styling */
  input, textarea {
    @apply bg-cyber-darkgray border border-cyber-red/30 focus:border-cyber-red focus:ring-1 focus:ring-cyber-red/50 text-white;
  }
}

@layer components {
  .cyber-panel {
    @apply bg-cyber-darkgray/90 backdrop-blur-sm border border-cyber-red/50 shadow-md shadow-cyber-red/20 relative overflow-hidden;
  }

  .cyber-panel::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-tr from-cyber-red/5 to-cyber-cyan/5 pointer-events-none;
  }

  .cyber-panel::after {
    content: "";
    @apply absolute inset-0 bg-[url('/matrix-bg.png')] bg-repeat opacity-5 pointer-events-none;
  }

  .cyber-scanline {
    @apply absolute inset-0 bg-gradient-to-b from-transparent via-white/5 to-transparent opacity-20 h-[20px] animate-scan pointer-events-none;
  }

  .cyber-header {
    @apply text-cyber-red font-mono uppercase tracking-wider border-b border-cyber-red/30 pb-1 mb-3 flex items-center;
  }

  .cyber-text {
    @apply text-white font-mono tracking-wide;
  }

  .cyber-alert {
    @apply border-l-4 border-cyber-red/70 bg-cyber-red/10 px-4 py-2 mb-4;
  }

  .matrix-bg {
    @apply fixed inset-0 bg-cyber-black z-[-1] overflow-hidden;
  }

  .glitch-wrapper {
    @apply relative inline-block;
  }

  .glitch-text {
    @apply relative inline-block;
  }

  .glitch-text::before,
  .glitch-text::after {
    @apply content-[attr(data-text)] absolute left-0 top-0 w-full h-full;
  }

  .glitch-text::before {
    @apply text-cyber-red left-[2px] text-opacity-70 animate-glitch-slow;
    clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
  }

  .glitch-text::after {
    @apply text-cyber-cyan left-[-2px] text-opacity-70 animate-glitch;
    clip-path: polygon(0 55%, 100% 55%, 100% 100%, 0 100%);
  }

  .digital-noise {
    @apply fixed inset-0 bg-[url('/noise.png')] bg-repeat opacity-[0.03] pointer-events-none z-10 mix-blend-overlay;
  }
}

#root {
  width: 100vw;
  min-height: 100vh;
  overflow: auto;
  padding: 0;
  margin: 0;
  max-width: none;
}
