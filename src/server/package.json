{"name": "r3b3l-4f-server", "version": "1.0.0", "description": "Secure server for R3B3L 4F with command execution and Ollama proxy", "main": "server.js", "scripts": {"start": "node server.js", "start:command": "node CommandBridge.js", "start:ollama": "node OllamaProxyServer.js", "dev": "nodemon server.js", "dev:command": "nodemon CommandBridge.js", "dev:ollama": "nodemon OllamaProxyServer.js"}, "dependencies": {"body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.0.1"}}