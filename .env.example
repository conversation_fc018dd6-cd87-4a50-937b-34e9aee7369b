# R3B3L 4F Frontend Configuration
# Copy this file to .env and modify as needed

# OpenAI API Configuration - Direct Integration
VITE_OPENAI_API_KEY=your-openai-api-key-here

# Supabase Configuration for Device Persistence (Optional)
VITE_SUPABASE_URL=your-supabase-url-here
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key-here

# Bright Data MCP Configuration
VITE_MCP_BASE_URL=https://brd-customer-hl_12345-zone-mcpserver
VITE_MCP_USERNAME=your-username-here
VITE_MCP_PASSWORD=your-password-here

# Bright Data Data Collector Configuration
VITE_BRIGHT_DATA_COLLECTOR_ID=your-collector-id-here
VITE_BRIGHT_DATA_API_KEY=your-api-key-here
